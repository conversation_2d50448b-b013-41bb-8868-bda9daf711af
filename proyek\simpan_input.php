<?php
require_once '../includes/session_manager.php';
check_session_auth('proyek');
require '../koneksi.php';

// Validasi method POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    header("Location: input_tugas.php");
    exit;
}

// Validasi dan sanitasi input
$nama_kegiatan = isset($_POST['nama_kegiatan']) ? trim($_POST['nama_kegiatan']) : '';
$deskripsi = isset($_POST['deskripsi']) ? trim($_POST['deskripsi']) : '';
$tanggal = isset($_POST['tgl']) ? $_POST['tgl'] : '';
$status = isset($_POST['status']) ? $_POST['status'] : 'proses';

// Validasi input
$errors = [];

if (empty($nama_kegiatan)) {
    $errors[] = "Nama kegiatan harus diisi";
} elseif (strlen($nama_kegiatan) < 5) {
    $errors[] = "Nama kegiatan minimal 5 karakter";
} elseif (strlen($nama_kegiatan) > 200) {
    $errors[] = "Nama kegiatan maksimal 200 karakter";
}

if (empty($deskripsi)) {
    $errors[] = "Deskripsi harus diisi";
} elseif (strlen($deskripsi) < 10) {
    $errors[] = "Deskripsi minimal 10 karakter";
} elseif (strlen($deskripsi) > 200) {
    $errors[] = "Deskripsi maksimal 200 karakter";
}

if (empty($tanggal)) {
    $errors[] = "Tanggal harus diisi";
} else {
    $selected_date = new DateTime($tanggal);
    $today = new DateTime();
    $max_date = new DateTime();
    $max_date->add(new DateInterval('P30D')); // 30 hari ke depan

    if ($selected_date > $max_date) {
        $errors[] = "Tanggal tidak boleh lebih dari 30 hari ke depan";
    }
}

if (!in_array($status, ['proses', 'selesai', 'batal'])) {
    $status = 'proses'; // Default fallback
}

// Jika ada error, redirect kembali dengan pesan error
if (!empty($errors)) {
    $error_message = implode("\\n", $errors);
    echo "<script>
        alert('Error:\\n$error_message');
        window.history.back();
    </script>";
    exit;
}

// Escape string untuk mencegah SQL injection
$nama_kegiatan = mysqli_real_escape_string($koneksi, $nama_kegiatan);
$deskripsi = mysqli_real_escape_string($koneksi, $deskripsi);
$tanggal = mysqli_real_escape_string($koneksi, $tanggal);
$status = mysqli_real_escape_string($koneksi, $status);

// Prepared statement untuk keamanan lebih baik
$stmt = mysqli_prepare($koneksi, "INSERT INTO tugas_proyek (nama_kegiatan, deskripsi, tgl, status, status_verifikasi, tanggal_submit) VALUES (?, ?, ?, ?, 'pending', NOW())");

if ($stmt) {
    mysqli_stmt_bind_param($stmt, "ssss", $nama_kegiatan, $deskripsi, $tanggal, $status);

    if (mysqli_stmt_execute($stmt)) {
        $task_id = mysqli_insert_id($koneksi);
        mysqli_stmt_close($stmt);

        echo "<script>
            alert('✅ Tugas berhasil disimpan!\\n\\nTugas Anda telah masuk ke antrian verifikasi dan akan ditampilkan setelah disetujui oleh admin.');
            window.location.href = 'input_tugas.php';
        </script>";
    } else {
        mysqli_stmt_close($stmt);
        echo "<script>
            alert('❌ Gagal menyimpan tugas. Silakan coba lagi.');
            window.history.back();
        </script>";
    }
} else {
    echo "<script>
        alert('❌ Terjadi kesalahan sistem. Silakan coba lagi.');
        window.history.back();
    </script>";
}
?>
