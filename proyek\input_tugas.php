<?php
require_once '../includes/session_manager.php';
check_session_auth('proyek');

$page_title = "Input Tugas Harian";
include 'includes/header/header.php';
?>

<?php include 'includes/sidebar/sidebar.php'; ?>

        <!-- Content Wrapper -->
        <div id="content-wrapper" class="d-flex flex-column">

            <!-- Main Content -->
            <div id="content">

<?php include 'includes/topbar/topbar.php'; ?>

                <!-- Begin Page Content -->
                <div class="container-fluid">

                    <!-- Page Heading -->
                    <div class="d-flex flex-column flex-sm-row align-items-start align-items-sm-center justify-content-between mb-3 mb-md-4">
                        <div class="mb-2 mb-sm-0">
                            <h1 class="h4 h-sm-3 mb-0 text-gray-800">
                                <i class="fas fa-plus-circle mr-2"></i>Input Tugas Harian
                            </h1>
                        </div>
                        <nav aria-label="breadcrumb" class="d-none d-md-block">
                            <ol class="breadcrumb mb-0 bg-transparent p-0">
                                <li class="breadcrumb-item">
                                    <a href="proyek.php" class="text-decoration-none">
                                        <i class="fas fa-home"></i>
                                        <span class="d-none d-lg-inline">Dashboard</span>
                                    </a>
                                </li>
                                <li class="breadcrumb-item">
                                    <span class="d-none d-lg-inline">Manajemen Tugas</span>
                                    <span class="d-lg-none">Tugas</span>
                                </li>
                                <li class="breadcrumb-item active">Input</li>
                            </ol>
                        </nav>
                    </div>

                    <!-- Info Alert -->
                    <div class="alert alert-info alert-dismissible fade show mb-3 mb-md-4" role="alert">
                        <div class="d-flex align-items-start">
                            <i class="fas fa-info-circle fa-lg mr-2 mr-md-3 mt-1"></i>
                            <div class="flex-grow-1">
                                <div class="d-block d-md-none">
                                    <strong>Info:</strong> Tugas akan diverifikasi admin sebelum ditampilkan.
                                </div>
                                <div class="d-none d-md-block">
                                    <strong>Informasi:</strong> Tugas yang Anda input akan masuk ke antrian verifikasi dan akan ditampilkan setelah disetujui oleh admin.
                                </div>
                            </div>
                        </div>
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>

                    <!-- Main Card -->
                    <div class="row justify-content-center">
                        <div class="col-12 col-md-10 col-lg-8 col-xl-7">
                            <div class="card shadow mb-4">
                                <div class="card-header py-3 px-3 px-md-4">
                                    <h6 class="m-0 font-weight-bold text-primary">
                                        <i class="fas fa-edit mr-2"></i>
                                        <span class="d-none d-sm-inline">Form Input Tugas Harian</span>
                                        <span class="d-sm-none">Input Tugas</span>
                                    </h6>
                                </div>
                                <div class="card-body p-3 p-md-4">
                                    <form id="tugasForm" action="simpan_input.php" method="post" novalidate>

                                        <!-- Nama Kegiatan -->
                                        <div class="form-group mb-3 mb-md-4">
                                            <label for="nama_kegiatan" class="font-weight-bold mb-2">
                                                <i class="fas fa-tasks mr-1 text-primary"></i>
                                                <span class="d-none d-sm-inline">Nama Kegiatan</span>
                                                <span class="d-sm-none">Kegiatan</span>
                                                <span class="text-danger">*</span>
                                            </label>
                                            <input type="text"
                                                   class="form-control form-control-lg"
                                                   id="nama_kegiatan"
                                                   name="nama_kegiatan"
                                                   placeholder="Contoh: Pengecoran Lantai 2"
                                                   maxlength="200"
                                                   required>
                                            <div class="invalid-feedback">
                                                <span class="d-block d-md-none">Nama kegiatan harus diisi (maks. 200 karakter)</span>
                                                <span class="d-none d-md-block">Nama kegiatan harus diisi dan maksimal 200 karakter.</span>
                                            </div>
                                            <small class="form-text text-muted d-flex justify-content-between align-items-center">
                                                <span class="d-none d-sm-inline">Karakter:</span>
                                                <span id="nama-counter" class="badge badge-light">0/200</span>
                                            </small>
                                        </div>

                                        <!-- Deskripsi Kegiatan -->
                                        <div class="form-group mb-3 mb-md-4">
                                            <label for="deskripsi" class="font-weight-bold mb-2">
                                                <i class="fas fa-align-left mr-1 text-primary"></i>
                                                <span class="d-none d-sm-inline">Deskripsi Pekerjaan</span>
                                                <span class="d-sm-none">Deskripsi</span>
                                                <span class="text-danger">*</span>
                                            </label>
                                            <textarea class="form-control"
                                                      id="deskripsi"
                                                      name="deskripsi"
                                                      rows="3"
                                                      placeholder="Detail progres pekerjaan, material, kendala..."
                                                      maxlength="200"
                                                      required></textarea>
                                            <div class="invalid-feedback">
                                                <span class="d-block d-md-none">Deskripsi harus diisi (maks. 200 karakter)</span>
                                                <span class="d-none d-md-block">Deskripsi pekerjaan harus diisi dan maksimal 200 karakter.</span>
                                            </div>
                                            <small class="form-text text-muted d-flex justify-content-between align-items-center">
                                                <span class="d-none d-sm-inline">Karakter:</span>
                                                <span id="deskripsi-counter" class="badge badge-light">0/200</span>
                                            </small>
                                        </div>

                                        <!-- Row untuk Tanggal dan Status -->
                                        <div class="row">
                                            <!-- Tanggal -->
                                            <div class="col-12 col-md-7 mb-3 mb-md-4">
                                                <label for="tgl" class="font-weight-bold mb-2">
                                                    <i class="fas fa-calendar-alt mr-1 text-primary"></i>
                                                    <span class="d-none d-sm-inline">Tanggal Pengerjaan</span>
                                                    <span class="d-sm-none">Tanggal</span>
                                                    <span class="text-danger">*</span>
                                                </label>
                                                <input type="date"
                                                       class="form-control form-control-lg"
                                                       id="tgl"
                                                       name="tgl"
                                                       required>
                                                <div class="invalid-feedback">
                                                    Tanggal harus diisi.
                                                </div>
                                                <small class="form-text text-muted d-none d-md-block">
                                                    Pilih tanggal pelaksanaan pekerjaan
                                                </small>
                                            </div>

                                            <!-- Status Progres -->
                                            <div class="col-12 col-md-5 mb-3 mb-md-4">
                                                <label for="status" class="font-weight-bold mb-2">
                                                    <i class="fas fa-flag mr-1 text-primary"></i>
                                                    <span class="d-none d-sm-inline">Status Progres</span>
                                                    <span class="d-sm-none">Status</span>
                                                </label>
                                                <select class="form-control form-control-lg" id="status" name="status">
                                                    <option value="proses" selected>Dalam Proses</option>
                                                    <option value="selesai">Selesai</option>
                                                    <option value="batal">Dibatalkan</option>
                                                </select>
                                                <small class="form-text text-muted d-none d-md-block">
                                                    Default: "Dalam Proses"
                                                </small>
                                            </div>
                                        </div>

                                        <!-- Action Buttons -->
                                        <div class="form-group mb-0">
                                            <!-- Mobile Layout -->
                                            <div class="d-block d-md-none">
                                                <div class="row">
                                                    <div class="col-12 mb-2">
                                                        <button type="submit" class="btn btn-primary btn-lg btn-block">
                                                            <i class="fas fa-save mr-2"></i>Simpan Tugas
                                                        </button>
                                                    </div>
                                                    <div class="col-6">
                                                        <button type="reset" class="btn btn-warning btn-block">
                                                            <i class="fas fa-undo mr-1"></i>Reset
                                                        </button>
                                                    </div>
                                                    <div class="col-6">
                                                        <a href="tugas_harian.php" class="btn btn-outline-secondary btn-block">
                                                            <i class="fas fa-list mr-1"></i>Daftar
                                                        </a>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Desktop Layout -->
                                            <div class="d-none d-md-flex justify-content-between align-items-center">
                                                <div>
                                                    <button type="submit" class="btn btn-primary btn-lg">
                                                        <i class="fas fa-save mr-2"></i>Simpan Tugas
                                                    </button>
                                                    <button type="reset" class="btn btn-warning btn-lg ml-2">
                                                        <i class="fas fa-undo mr-2"></i>Reset Form
                                                    </button>
                                                </div>
                                                <div>
                                                    <a href="tugas_harian.php" class="btn btn-outline-secondary">
                                                        <i class="fas fa-list mr-1"></i>Lihat Daftar Tugas
                                                    </a>
                                                </div>
                                            </div>
                                        </div>

                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
                <!-- /.container-fluid -->
            </div>
            <!-- End of Main Content -->

<?php include 'includes/footer/footer.php'; ?>

<!-- Custom JavaScript for Input Tugas -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Set tanggal hari ini sebagai default
    const today = new Date().toISOString().split('T')[0];
    document.getElementById('tgl').value = today;

    // Character counter untuk nama kegiatan
    const namaInput = document.getElementById('nama_kegiatan');
    const namaCounter = document.getElementById('nama-counter');

    namaInput.addEventListener('input', function() {
        const length = this.value.length;
        namaCounter.textContent = length + '/200';

        // Update badge color based on character count
        namaCounter.className = 'badge ';
        if (length > 180) {
            namaCounter.className += 'badge-danger';
        } else if (length > 150) {
            namaCounter.className += 'badge-warning';
        } else if (length > 0) {
            namaCounter.className += 'badge-info';
        } else {
            namaCounter.className += 'badge-light';
        }
    });

    // Character counter untuk deskripsi
    const deskripsiInput = document.getElementById('deskripsi');
    const deskripsiCounter = document.getElementById('deskripsi-counter');

    deskripsiInput.addEventListener('input', function() {
        const length = this.value.length;
        deskripsiCounter.textContent = length + '/200';

        // Update badge color based on character count
        deskripsiCounter.className = 'badge ';
        if (length > 180) {
            deskripsiCounter.className += 'badge-danger';
        } else if (length > 150) {
            deskripsiCounter.className += 'badge-warning';
        } else if (length > 0) {
            deskripsiCounter.className += 'badge-info';
        } else {
            deskripsiCounter.className += 'badge-light';
        }
    });

    // Form validation
    const form = document.getElementById('tugasForm');

    form.addEventListener('submit', function(event) {
        event.preventDefault();
        event.stopPropagation();

        // Reset previous validation states
        form.classList.remove('was-validated');

        // Custom validation
        let isValid = true;

        // Validate nama kegiatan
        const namaKegiatan = namaInput.value.trim();
        if (namaKegiatan.length < 5) {
            namaInput.setCustomValidity('Nama kegiatan minimal 5 karakter');
            isValid = false;
        } else if (namaKegiatan.length > 200) {
            namaInput.setCustomValidity('Nama kegiatan maksimal 200 karakter');
            isValid = false;
        } else {
            namaInput.setCustomValidity('');
        }

        // Validate deskripsi
        const deskripsi = deskripsiInput.value.trim();
        if (deskripsi.length < 10) {
            deskripsiInput.setCustomValidity('Deskripsi minimal 10 karakter');
            isValid = false;
        } else if (deskripsi.length > 200) {
            deskripsiInput.setCustomValidity('Deskripsi maksimal 200 karakter');
            isValid = false;
        } else {
            deskripsiInput.setCustomValidity('');
        }

        // Validate tanggal
        const tanggal = document.getElementById('tgl').value;
        const selectedDate = new Date(tanggal);
        const today = new Date();
        const maxDate = new Date();
        maxDate.setDate(today.getDate() + 30); // Maksimal 30 hari ke depan

        if (!tanggal) {
            document.getElementById('tgl').setCustomValidity('Tanggal harus diisi');
            isValid = false;
        } else if (selectedDate > maxDate) {
            document.getElementById('tgl').setCustomValidity('Tanggal tidak boleh lebih dari 30 hari ke depan');
            isValid = false;
        } else {
            document.getElementById('tgl').setCustomValidity('');
        }

        // Show validation feedback
        form.classList.add('was-validated');

        if (isValid && form.checkValidity()) {
            // Show loading state
            const submitBtn = form.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Menyimpan...';
            submitBtn.disabled = true;

            // Submit form
            setTimeout(() => {
                form.submit();
            }, 500);
        } else {
            // Scroll to first invalid field with responsive offset
            const firstInvalid = form.querySelector(':invalid');
            if (firstInvalid) {
                const isMobile = window.innerWidth < 768;
                const offset = isMobile ? 100 : 150;

                const elementPosition = firstInvalid.getBoundingClientRect().top + window.pageYOffset;
                const offsetPosition = elementPosition - offset;

                window.scrollTo({
                    top: offsetPosition,
                    behavior: 'smooth'
                });

                // Focus with delay for better UX on mobile
                setTimeout(() => {
                    firstInvalid.focus();
                }, 300);
            }
        }
    });

    // Reset form handler
    form.addEventListener('reset', function() {
        setTimeout(() => {
            form.classList.remove('was-validated');
            namaCounter.textContent = '0/200';
            deskripsiCounter.textContent = '0/200';
            namaCounter.className = 'badge badge-light';
            deskripsiCounter.className = 'badge badge-light';
            document.getElementById('tgl').value = today;
        }, 10);
    });

    // Auto-resize textarea with responsive handling
    deskripsiInput.addEventListener('input', function() {
        this.style.height = 'auto';
        const newHeight = Math.min(this.scrollHeight, window.innerWidth < 768 ? 120 : 200);
        this.style.height = newHeight + 'px';
    });

    // Handle orientation change on mobile
    window.addEventListener('orientationchange', function() {
        setTimeout(() => {
            // Recalculate textarea height
            deskripsiInput.style.height = 'auto';
            const newHeight = Math.min(deskripsiInput.scrollHeight, window.innerWidth < 768 ? 120 : 200);
            deskripsiInput.style.height = newHeight + 'px';
        }, 100);
    });

    // Improve mobile form interaction
    if (window.innerWidth < 768) {
        // Add touch-friendly focus handling
        const inputs = form.querySelectorAll('input, textarea, select');
        inputs.forEach(input => {
            input.addEventListener('focus', function() {
                setTimeout(() => {
                    this.scrollIntoView({ behavior: 'smooth', block: 'center' });
                }, 300);
            });
        });
    }
});
</script>

<!-- Custom CSS for Enhanced Responsiveness -->
<style>
/* Mobile-first responsive improvements */
@media (max-width: 767.98px) {
    .container-fluid {
        padding-left: 15px;
        padding-right: 15px;
    }

    .card-body {
        padding: 1rem !important;
    }

    .form-control-lg {
        font-size: 1rem;
        padding: 0.75rem;
    }

    .btn-lg {
        padding: 0.75rem 1rem;
        font-size: 1rem;
    }

    /* Improve touch targets */
    .form-control, .btn {
        min-height: 44px;
    }

    /* Better spacing for mobile */
    .form-group {
        margin-bottom: 1.5rem;
    }

    /* Responsive badges */
    .badge {
        font-size: 0.75rem;
        padding: 0.375rem 0.5rem;
    }

    /* Alert improvements */
    .alert {
        padding: 0.75rem;
        margin-bottom: 1rem;
    }

    /* Better button spacing */
    .btn-block + .btn-block {
        margin-top: 0.5rem;
    }
}

/* Tablet improvements */
@media (min-width: 768px) and (max-width: 991.98px) {
    .card-body {
        padding: 1.5rem !important;
    }

    .form-group {
        margin-bottom: 1.75rem;
    }
}

/* Desktop improvements */
@media (min-width: 992px) {
    .card-body {
        padding: 2rem !important;
    }

    .form-group {
        margin-bottom: 2rem;
    }
}

/* Focus improvements for accessibility */
.form-control:focus {
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    border-color: #80bdff;
}

/* Loading state improvements */
.btn:disabled {
    opacity: 0.65;
    cursor: not-allowed;
}

/* Character counter improvements */
.badge {
    transition: all 0.2s ease-in-out;
}

/* Smooth transitions */
.form-control, .btn {
    transition: all 0.15s ease-in-out;
}

/* Better invalid feedback positioning */
.invalid-feedback {
    display: block;
    margin-top: 0.25rem;
}

/* Responsive text sizing */
@media (max-width: 575.98px) {
    .h4 {
        font-size: 1.25rem;
    }

    .font-weight-bold {
        font-size: 0.9rem;
    }

    .form-text {
        font-size: 0.8rem;
    }
}
</style>