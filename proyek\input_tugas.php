<?php
require_once '../includes/session_manager.php';
check_session_auth('proyek');

$page_title = "Input Tugas Harian";
include 'includes/header/header.php';
?>

<?php include 'includes/sidebar/sidebar.php'; ?>

        <!-- Content Wrapper -->
        <div id="content-wrapper" class="d-flex flex-column">

            <!-- Main Content -->
            <div id="content">

<?php include 'includes/topbar/topbar.php'; ?>

                <!-- Begin Page Content -->
                <div class="container-fluid">

                    <!-- Page Heading -->
                    <div class="d-sm-flex align-items-center justify-content-between mb-4">
                        <h1 class="h3 mb-0 text-gray-800">
                            <i class="fas fa-plus-circle mr-2"></i>Input Tugas Harian
                        </h1>
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb mb-0 bg-transparent">
                                <li class="breadcrumb-item">
                                    <a href="proyek.php" class="text-decoration-none">
                                        <i class="fas fa-home"></i> Dashboard
                                    </a>
                                </li>
                                <li class="breadcrumb-item">Manajemen Tugas</li>
                                <li class="breadcrumb-item active">Input Tugas</li>
                            </ol>
                        </nav>
                    </div>

                    <!-- Info Alert -->
                    <div class="alert alert-info alert-dismissible fade show mb-4" role="alert">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-info-circle fa-lg mr-3"></i>
                            <div>
                                <strong>Informasi:</strong> Tugas yang Anda input akan masuk ke antrian verifikasi dan akan ditampilkan setelah disetujui oleh admin.
                            </div>
                        </div>
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>

                    <!-- Main Card -->
                    <div class="row justify-content-center">
                        <div class="col-lg-8">
                            <div class="card shadow mb-4">
                                <div class="card-header py-3">
                                    <h6 class="m-0 font-weight-bold text-primary">
                                        <i class="fas fa-edit mr-2"></i>Form Input Tugas Harian
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <form id="tugasForm" action="simpan_input.php" method="post" novalidate>

                                        <!-- Nama Kegiatan -->
                                        <div class="form-group">
                                            <label for="nama_kegiatan" class="font-weight-bold">
                                                <i class="fas fa-tasks mr-1 text-primary"></i>Nama Kegiatan
                                                <span class="text-danger">*</span>
                                            </label>
                                            <input type="text"
                                                   class="form-control"
                                                   id="nama_kegiatan"
                                                   name="nama_kegiatan"
                                                   placeholder="Contoh: Pengecoran Lantai 2, Pemasangan Rangka Atap, dll"
                                                   maxlength="200"
                                                   required>
                                            <div class="invalid-feedback">
                                                Nama kegiatan harus diisi dan maksimal 200 karakter.
                                            </div>
                                            <small class="form-text text-muted">
                                                <span id="nama-counter">0</span>/200 karakter
                                            </small>
                                        </div>

                                        <!-- Deskripsi Kegiatan -->
                                        <div class="form-group">
                                            <label for="deskripsi" class="font-weight-bold">
                                                <i class="fas fa-align-left mr-1 text-primary"></i>Deskripsi Pekerjaan
                                                <span class="text-danger">*</span>
                                            </label>
                                            <textarea class="form-control"
                                                      id="deskripsi"
                                                      name="deskripsi"
                                                      rows="4"
                                                      placeholder="Deskripsikan detail progres pekerjaan di lapangan, material yang digunakan, kendala yang dihadapi, dll"
                                                      maxlength="200"
                                                      required></textarea>
                                            <div class="invalid-feedback">
                                                Deskripsi pekerjaan harus diisi dan maksimal 200 karakter.
                                            </div>
                                            <small class="form-text text-muted">
                                                <span id="deskripsi-counter">0</span>/200 karakter
                                            </small>
                                        </div>

                                        <!-- Tanggal -->
                                        <div class="form-group">
                                            <label for="tgl" class="font-weight-bold">
                                                <i class="fas fa-calendar-alt mr-1 text-primary"></i>Tanggal Pengerjaan
                                                <span class="text-danger">*</span>
                                            </label>
                                            <input type="date"
                                                   class="form-control"
                                                   id="tgl"
                                                   name="tgl"
                                                   required>
                                            <div class="invalid-feedback">
                                                Tanggal pengerjaan harus diisi.
                                            </div>
                                            <small class="form-text text-muted">
                                                Pilih tanggal pelaksanaan pekerjaan
                                            </small>
                                        </div>

                                        <!-- Status Progres (Optional - untuk future use) -->
                                        <div class="form-group">
                                            <label for="status" class="font-weight-bold">
                                                <i class="fas fa-flag mr-1 text-primary"></i>Status Progres
                                            </label>
                                            <select class="form-control" id="status" name="status">
                                                <option value="proses" selected>Dalam Proses</option>
                                                <option value="selesai">Selesai</option>
                                                <option value="batal">Dibatalkan</option>
                                            </select>
                                            <small class="form-text text-muted">
                                                Status default adalah "Dalam Proses"
                                            </small>
                                        </div>

                                        <!-- Action Buttons -->
                                        <div class="form-group mb-0">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <div>
                                                    <button type="submit" class="btn btn-primary btn-lg">
                                                        <i class="fas fa-save mr-2"></i>Simpan Tugas
                                                    </button>
                                                    <button type="reset" class="btn btn-warning btn-lg ml-2">
                                                        <i class="fas fa-undo mr-2"></i>Reset Form
                                                    </button>
                                                </div>
                                                <div>
                                                    <a href="tugas_harian.php" class="btn btn-outline-secondary">
                                                        <i class="fas fa-list mr-1"></i>Lihat Daftar Tugas
                                                    </a>
                                                </div>
                                            </div>
                                        </div>

                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
                <!-- /.container-fluid -->
            </div>
            <!-- End of Main Content -->

<?php include 'includes/footer/footer.php'; ?>

<!-- Custom JavaScript for Input Tugas -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Set tanggal hari ini sebagai default
    const today = new Date().toISOString().split('T')[0];
    document.getElementById('tgl').value = today;

    // Character counter untuk nama kegiatan
    const namaInput = document.getElementById('nama_kegiatan');
    const namaCounter = document.getElementById('nama-counter');

    namaInput.addEventListener('input', function() {
        const length = this.value.length;
        namaCounter.textContent = length;

        if (length > 180) {
            namaCounter.style.color = '#e74a3b';
        } else if (length > 150) {
            namaCounter.style.color = '#f6c23e';
        } else {
            namaCounter.style.color = '#5a5c69';
        }
    });

    // Character counter untuk deskripsi
    const deskripsiInput = document.getElementById('deskripsi');
    const deskripsiCounter = document.getElementById('deskripsi-counter');

    deskripsiInput.addEventListener('input', function() {
        const length = this.value.length;
        deskripsiCounter.textContent = length;

        if (length > 180) {
            deskripsiCounter.style.color = '#e74a3b';
        } else if (length > 150) {
            deskripsiCounter.style.color = '#f6c23e';
        } else {
            deskripsiCounter.style.color = '#5a5c69';
        }
    });

    // Form validation
    const form = document.getElementById('tugasForm');

    form.addEventListener('submit', function(event) {
        event.preventDefault();
        event.stopPropagation();

        // Reset previous validation states
        form.classList.remove('was-validated');

        // Custom validation
        let isValid = true;

        // Validate nama kegiatan
        const namaKegiatan = namaInput.value.trim();
        if (namaKegiatan.length < 5) {
            namaInput.setCustomValidity('Nama kegiatan minimal 5 karakter');
            isValid = false;
        } else if (namaKegiatan.length > 200) {
            namaInput.setCustomValidity('Nama kegiatan maksimal 200 karakter');
            isValid = false;
        } else {
            namaInput.setCustomValidity('');
        }

        // Validate deskripsi
        const deskripsi = deskripsiInput.value.trim();
        if (deskripsi.length < 10) {
            deskripsiInput.setCustomValidity('Deskripsi minimal 10 karakter');
            isValid = false;
        } else if (deskripsi.length > 200) {
            deskripsiInput.setCustomValidity('Deskripsi maksimal 200 karakter');
            isValid = false;
        } else {
            deskripsiInput.setCustomValidity('');
        }

        // Validate tanggal
        const tanggal = document.getElementById('tgl').value;
        const selectedDate = new Date(tanggal);
        const today = new Date();
        const maxDate = new Date();
        maxDate.setDate(today.getDate() + 30); // Maksimal 30 hari ke depan

        if (!tanggal) {
            document.getElementById('tgl').setCustomValidity('Tanggal harus diisi');
            isValid = false;
        } else if (selectedDate > maxDate) {
            document.getElementById('tgl').setCustomValidity('Tanggal tidak boleh lebih dari 30 hari ke depan');
            isValid = false;
        } else {
            document.getElementById('tgl').setCustomValidity('');
        }

        // Show validation feedback
        form.classList.add('was-validated');

        if (isValid && form.checkValidity()) {
            // Show loading state
            const submitBtn = form.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Menyimpan...';
            submitBtn.disabled = true;

            // Submit form
            setTimeout(() => {
                form.submit();
            }, 500);
        } else {
            // Scroll to first invalid field
            const firstInvalid = form.querySelector(':invalid');
            if (firstInvalid) {
                firstInvalid.scrollIntoView({ behavior: 'smooth', block: 'center' });
                firstInvalid.focus();
            }
        }
    });

    // Reset form handler
    form.addEventListener('reset', function() {
        setTimeout(() => {
            form.classList.remove('was-validated');
            namaCounter.textContent = '0';
            deskripsiCounter.textContent = '0';
            namaCounter.style.color = '#5a5c69';
            deskripsiCounter.style.color = '#5a5c69';
            document.getElementById('tgl').value = today;
        }, 10);
    });

    // Auto-resize textarea
    deskripsiInput.addEventListener('input', function() {
        this.style.height = 'auto';
        this.style.height = (this.scrollHeight) + 'px';
    });
});
</script>